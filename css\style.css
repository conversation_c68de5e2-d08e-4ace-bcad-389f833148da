/* Tshirtk - موقع بيع التيشيرتات */
/* تخصيص الخطوط والألوان */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #e67e22;
    --accent-color: #3498db;
    --text-dark: #2c3e50;
    --text-light: #7f8c8d;
    --bg-light: #f8f9fa;
    --white: #ffffff;
    --border-color: #dee2e6;
}

/* إعدادات RTL */
body {
    direction: rtl;
    text-align: right;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
}

/* تخصيص الهيدر */
.navbar-brand {
    font-weight: bold;
    font-size: 1.8rem;
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 10px;
    color: var(--text-dark) !important;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--accent-color) !important;
}

/* قسم Hero */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: white;
    padding: 100px 0;
    min-height: 500px;
    display: flex;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 20px;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.btn-hero {
    background-color: var(--secondary-color);
    border: none;
    padding: 15px 40px;
    font-size: 1.2rem;
    font-weight: bold;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-hero:hover {
    background-color: #d35400;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

/* كروت المنتجات */
.product-card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 30px;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.product-image {
    height: 250px;
    object-fit: cover;
    width: 100%;
}

.product-title {
    font-weight: bold;
    color: var(--text-dark);
    margin-bottom: 10px;
}

.product-price {
    font-size: 1.3rem;
    font-weight: bold;
    color: var(--secondary-color);
    margin-bottom: 15px;
}

.btn-add-cart {
    background-color: var(--accent-color);
    border: none;
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.btn-add-cart:hover {
    background-color: #2980b9;
    transform: scale(1.05);
}

/* قسم المميزات */
.features-section {
    background-color: var(--bg-light);
    padding: 80px 0;
}

.feature-icon {
    font-size: 3rem;
    color: var(--accent-color);
    margin-bottom: 20px;
}

.feature-title {
    font-weight: bold;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.feature-description {
    color: var(--text-light);
    line-height: 1.8;
}

/* الفوتر */
.footer {
    background-color: var(--primary-color);
    color: white;
    padding: 50px 0 20px;
}

.footer h5 {
    margin-bottom: 20px;
    font-weight: bold;
}

.footer a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: white;
}

.social-icons a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-color: var(--accent-color);
    color: white;
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    margin: 0 5px;
    transition: all 0.3s ease;
}

.social-icons a:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
}

/* صفحة العربة */
.cart-item {
    border-bottom: 1px solid var(--border-color);
    padding: 20px 0;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-total {
    background-color: var(--bg-light);
    padding: 30px;
    border-radius: 10px;
    margin-top: 30px;
}

/* نموذج الدفع */
.checkout-form {
    background-color: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.form-control {
    border-radius: 8px;
    border: 2px solid var(--border-color);
    padding: 12px 15px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* تأثيرات إضافية */
.section-title {
    font-size: 2.5rem;
    font-weight: bold;
    text-align: center;
    margin-bottom: 50px;
    color: var(--text-dark);
}

.section-subtitle {
    text-align: center;
    color: var(--text-light);
    margin-bottom: 60px;
    font-size: 1.1rem;
}

/* تجاوب الموبايل */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .navbar-brand {
        font-size: 1.5rem;
    }
}

/* أنيميشن للتحميل */
.fade-in {
    animation: fadeIn 0.8s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

/* شارة العربة */
.cart-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--secondary-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.cart-icon {
    position: relative;
    display: inline-block;
}
