/* Tshirtk - موقع بيع التيشيرتات */
/* تخصيص الخطوط والألوان */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #e67e22;
    --accent-color: #3498db;
    --text-dark: #2c3e50;
    --text-light: #7f8c8d;
    --bg-light: #f8f9fa;
    --white: #ffffff;
    --border-color: #dee2e6;
}

/* إعدادات RTL */
body {
    direction: rtl;
    text-align: right;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
}

/* تخصيص الهيدر */
.navbar-brand {
    font-weight: bold;
    font-size: 1.8rem;
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 10px;
    color: var(--text-dark) !important;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--accent-color) !important;
}

/* قسم Hero */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: white;
    padding: 120px 0;
    min-height: 600px;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: bold;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
}

.hero-title {
    font-size: 3.8rem;
    font-weight: bold;
    margin-bottom: 25px;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.4rem;
    margin-bottom: 35px;
    opacity: 0.95;
    line-height: 1.6;
}

.hero-features {
    margin: 30px 0;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-size: 1.1rem;
}

.feature-item i {
    margin-left: 12px;
    font-size: 1.2rem;
}

.hero-buttons {
    margin-top: 40px;
}

.btn-hero {
    background-color: var(--secondary-color);
    border: none;
    padding: 18px 45px;
    font-size: 1.3rem;
    font-weight: bold;
    border-radius: 50px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.btn-hero:hover {
    background-color: #d35400;
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.3);
}

.btn-outline-light {
    border: 2px solid rgba(255,255,255,0.8);
    color: white;
    padding: 18px 35px;
    font-size: 1.1rem;
    font-weight: bold;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    background-color: white;
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* صورة Hero */
.hero-image-container {
    position: relative;
    display: inline-block;
}

.hero-image {
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    transition: transform 0.3s ease;
}

.hero-image:hover {
    transform: scale(1.02);
}

.hero-image-overlay {
    position: absolute;
    bottom: 20px;
    right: 20px;
}

.price-tag {
    background: white;
    padding: 15px 20px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    text-align: center;
}

.old-price {
    display: block;
    color: #999;
    text-decoration: line-through;
    font-size: 1rem;
}

.new-price {
    display: block;
    color: var(--secondary-color);
    font-size: 1.5rem;
    font-weight: bold;
}

/* كروت المنتجات */
.product-card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 30px;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.product-image {
    height: 250px;
    object-fit: cover;
    width: 100%;
}

.product-title {
    font-weight: bold;
    color: var(--text-dark);
    margin-bottom: 10px;
}

.product-price {
    font-size: 1.3rem;
    font-weight: bold;
    color: var(--secondary-color);
    margin-bottom: 15px;
}

.btn-add-cart {
    background-color: var(--accent-color);
    border: none;
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.btn-add-cart:hover {
    background-color: #2980b9;
    transform: scale(1.05);
}

/* قسم المميزات */
.features-section {
    background-color: var(--bg-light);
    padding: 80px 0;
}

.feature-icon {
    font-size: 3rem;
    color: var(--accent-color);
    margin-bottom: 20px;
}

.feature-title {
    font-weight: bold;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.feature-description {
    color: var(--text-light);
    line-height: 1.8;
}

/* قسم الإحصائيات */
.stats-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.5;
}

.stat-item {
    position: relative;
    z-index: 2;
    padding: 30px 20px;
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-10px);
}

.stat-icon {
    font-size: 3.5rem;
    margin-bottom: 20px;
    color: var(--secondary-color);
}

.stat-number {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 10px;
    color: white;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 500;
}

/* هيدر صفحة المنتجات */
.products-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    position: relative;
    overflow: hidden;
}

.products-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.products-count-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.count-number {
    font-size: 3rem;
    font-weight: bold;
    color: white;
    margin-bottom: 10px;
}

.count-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
    font-weight: 500;
}

/* الفوتر */
.footer {
    background-color: var(--primary-color);
    color: white;
    padding: 50px 0 20px;
}

.footer h5 {
    margin-bottom: 20px;
    font-weight: bold;
}

.footer a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: white;
}

.social-icons a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-color: var(--accent-color);
    color: white;
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    margin: 0 5px;
    transition: all 0.3s ease;
}

.social-icons a:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
}

/* صفحة العربة */
.cart-item {
    border-bottom: 1px solid var(--border-color);
    padding: 20px 0;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-total {
    background-color: var(--bg-light);
    padding: 30px;
    border-radius: 10px;
    margin-top: 30px;
}

/* نموذج الدفع */
.checkout-form {
    background-color: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.form-control {
    border-radius: 8px;
    border: 2px solid var(--border-color);
    padding: 12px 15px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* تأثيرات إضافية */
.section-title {
    font-size: 2.5rem;
    font-weight: bold;
    text-align: center;
    margin-bottom: 50px;
    color: var(--text-dark);
}

.section-subtitle {
    text-align: center;
    color: var(--text-light);
    margin-bottom: 60px;
    font-size: 1.1rem;
}

/* تجاوب الموبايل */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .navbar-brand {
        font-size: 1.5rem;
    }
}

/* صفحة العربة */
.cart-item {
    border-bottom: 1px solid var(--border-color);
    padding: 20px 0;
    transition: background-color 0.3s ease;
}

.cart-item:hover {
    background-color: rgba(52, 152, 219, 0.05);
    border-radius: 10px;
    margin: 0 -15px;
    padding: 20px 15px;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-total {
    background: linear-gradient(135deg, var(--bg-light) 0%, rgba(52, 152, 219, 0.1) 100%);
    padding: 30px;
    border-radius: 15px;
    margin-top: 30px;
    border: 1px solid rgba(52, 152, 219, 0.2);
}

/* شارات الثقة */
.trust-badges {
    background-color: var(--bg-light);
    padding: 20px;
    border-radius: 10px;
}

.trust-item {
    text-align: center;
    padding: 10px 5px;
}

.trust-item i {
    font-size: 1.5rem;
    margin-bottom: 5px;
    display: block;
}

.trust-item small {
    font-size: 0.8rem;
    color: var(--text-light);
    font-weight: 500;
}

/* تنبيه الشحن المجاني */
#shippingAlert {
    border-radius: 10px;
    border: none;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-left: 4px solid #28a745;
}

/* هيدر صفحة الدفع */
.checkout-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    position: relative;
    overflow: hidden;
}

.checkout-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="checkout-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23checkout-pattern)"/></svg>');
    opacity: 0.3;
}

/* مؤشر التقدم */
.checkout-progress {
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 500px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.step-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.progress-step.completed .step-icon {
    background-color: var(--secondary-color);
    color: white;
}

.progress-step.active .step-icon {
    background-color: white;
    color: var(--primary-color);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.step-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.progress-step.completed .step-label,
.progress-step.active .step-label {
    color: white;
    font-weight: bold;
}

.progress-line {
    width: 80px;
    height: 2px;
    background-color: rgba(255, 255, 255, 0.3);
    margin: 0 20px;
    margin-bottom: 30px;
}

.progress-line.completed {
    background-color: var(--secondary-color);
}

/* نموذج الدفع */
.checkout-form {
    background-color: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.form-control {
    border-radius: 8px;
    border: 2px solid var(--border-color);
    padding: 12px 15px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* تأثيرات إضافية */
.section-title {
    font-size: 2.5rem;
    font-weight: bold;
    text-align: center;
    margin-bottom: 50px;
    color: var(--text-dark);
}

.section-subtitle {
    text-align: center;
    color: var(--text-light);
    margin-bottom: 60px;
    font-size: 1.1rem;
}

/* صفحة التواصل */
.contact-info h6 {
    color: var(--text-dark);
    font-weight: bold;
    margin-bottom: 10px;
}

.contact-info p {
    margin-bottom: 0;
    line-height: 1.6;
}

/* خريطة وهمية */
.map-container {
    margin-top: 20px;
}

.map-placeholder {
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
    color: white;
    padding: 40px 20px;
    border-radius: 15px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.map-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="map-pattern" width="10" height="10" patternUnits="userSpaceOnUse"><circle cx="5" cy="5" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23map-pattern)"/></svg>');
    opacity: 0.3;
}

.map-placeholder i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: var(--secondary-color);
    position: relative;
    z-index: 2;
}

.map-placeholder h6,
.map-placeholder p {
    position: relative;
    z-index: 2;
    margin-bottom: 10px;
}

.map-placeholder:hover {
    transform: translateY(-5px);
    transition: transform 0.3s ease;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* أنيميشن للتحميل */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.fade-in.show {
    opacity: 1;
    transform: translateY(0);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

/* شاشة التحميل */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.5s ease;
}

.loading-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: white;
    font-size: 1.2rem;
    font-weight: bold;
    margin-top: 20px;
    text-align: center;
}

/* تأثير ripple للأزرار */
.btn {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* تحسين الأداء */
.product-image,
.hero-image {
    will-change: transform;
}

.product-card,
.stat-item,
.feature-item {
    will-change: transform;
}

/* تحسينات إضافية للتجاوب */
@media (max-width: 576px) {
    .hero-section {
        padding: 60px 0;
        min-height: 400px;
    }

    .hero-title {
        font-size: 2.2rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .checkout-form {
        padding: 20px;
    }

    .cart-total {
        padding: 20px;
    }

    .product-card {
        margin-bottom: 20px;
    }

    .checkout-progress {
        flex-direction: column;
        gap: 20px;
    }

    .progress-line {
        width: 2px;
        height: 40px;
        margin: 10px 0;
    }

    .step-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}

/* تحسين أزرار الهاتف المحمول */
@media (max-width: 768px) {
    .btn-hero {
        padding: 12px 30px;
        font-size: 1.1rem;
    }

    .btn-add-cart {
        padding: 8px 20px;
        font-size: 0.9rem;
    }

    .social-icons a {
        width: 35px;
        height: 35px;
        line-height: 35px;
        font-size: 0.9rem;
    }
}

/* شارة العربة */
.cart-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--secondary-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.cart-icon {
    position: relative;
    display: inline-block;
}

/* شارات المنتجات */
.product-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
}

.product-bestseller {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 2;
}

.product-badge .badge,
.product-bestseller .badge {
    font-size: 0.75rem;
    padding: 5px 8px;
}

/* تقييم المنتجات */
.product-rating {
    font-size: 0.9rem;
}

.product-rating i {
    font-size: 0.8rem;
}

/* تحسين صور المنتجات */
.product-image {
    height: 280px;
    object-fit: cover;
    width: 100%;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

/* تأثير overlay للصور */
.product-card .position-relative::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 0%, rgba(0,0,0,0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.product-card:hover .position-relative::before {
    opacity: 1;
}
