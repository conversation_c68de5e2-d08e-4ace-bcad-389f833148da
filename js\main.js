// Tshirtk - JavaScript الرئيسي

// بيانات المنتجات التجريبية
const products = [
    {
        id: 1,
        name: "تيشيرت قطن رجالي كلاسيكي",
        price: 89,
        image: "https://via.placeholder.com/300x300/e74c3c/ffffff?text=تيشيرت+رجالي",
        description: "تيشيرت قطن 100% مريح وعملي للاستخدام اليومي",
        colors: ["أسود", "أبيض", "أزرق", "رمادي"],
        sizes: ["S", "M", "L", "XL", "XXL"]
    },
    {
        id: 2,
        name: "تيشيرت نسائي عصري",
        price: 75,
        image: "https://via.placeholder.com/300x300/9b59b6/ffffff?text=تيشيرت+نسائي",
        description: "تيشيرت نسائي بتصميم عصري ومريح",
        colors: ["وردي", "أبيض", "أسود", "بنفسجي"],
        sizes: ["XS", "S", "M", "L", "XL"]
    },
    {
        id: 3,
        name: "تيشيرت رياضي للجري",
        price: 95,
        image: "https://via.placeholder.com/300x300/2ecc71/ffffff?text=تيشيرت+رياضي",
        description: "تيشيرت رياضي مصنوع من قماش يمتص العرق",
        colors: ["أخضر", "أزرق", "أسود", "أحمر"],
        sizes: ["S", "M", "L", "XL"]
    },
    {
        id: 4,
        name: "تيشيرت بولو أنيق",
        price: 120,
        image: "https://via.placeholder.com/300x300/34495e/ffffff?text=بولو+أنيق",
        description: "تيشيرت بولو كلاسيكي مناسب للمناسبات الرسمية",
        colors: ["كحلي", "أبيض", "رمادي", "بني"],
        sizes: ["S", "M", "L", "XL", "XXL"]
    },
    {
        id: 5,
        name: "تيشيرت بطبعة فنية",
        price: 85,
        image: "https://via.placeholder.com/300x300/f39c12/ffffff?text=طبعة+فنية",
        description: "تيشيرت بطبعة فنية مميزة وألوان زاهية",
        colors: ["أصفر", "أبيض", "أسود"],
        sizes: ["S", "M", "L", "XL"]
    },
    {
        id: 6,
        name: "تيشيرت أطفال مرح",
        price: 65,
        image: "https://via.placeholder.com/300x300/e67e22/ffffff?text=تيشيرت+أطفال",
        description: "تيشيرت أطفال بألوان مرحة وتصميم جذاب",
        colors: ["برتقالي", "أزرق", "أخضر", "وردي"],
        sizes: ["XS", "S", "M", "L"]
    }
];

// إدارة العربة
let cart = JSON.parse(localStorage.getItem('tshirtk_cart')) || [];

// تحديث عداد العربة
function updateCartCount() {
    const cartCount = document.getElementById('cartCount');
    if (cartCount) {
        const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
        cartCount.textContent = totalItems;
        cartCount.style.display = totalItems > 0 ? 'flex' : 'none';
    }
}

// إضافة منتج للعربة
function addToCart(productId, quantity = 1, selectedColor = null, selectedSize = null) {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    const existingItem = cart.find(item => 
        item.id === productId && 
        item.color === selectedColor && 
        item.size === selectedSize
    );

    if (existingItem) {
        existingItem.quantity += quantity;
    } else {
        cart.push({
            id: productId,
            name: product.name,
            price: product.price,
            image: product.image,
            quantity: quantity,
            color: selectedColor || product.colors[0],
            size: selectedSize || product.sizes[0]
        });
    }

    localStorage.setItem('tshirtk_cart', JSON.stringify(cart));
    updateCartCount();
    
    // إظهار رسالة نجاح
    showNotification('تم إضافة المنتج إلى العربة بنجاح!', 'success');
}

// حذف منتج من العربة
function removeFromCart(index) {
    cart.splice(index, 1);
    localStorage.setItem('tshirtk_cart', JSON.stringify(cart));
    updateCartCount();
    if (typeof displayCartItems === 'function') {
        displayCartItems();
    }
}

// تحديث كمية المنتج
function updateQuantity(index, newQuantity) {
    if (newQuantity <= 0) {
        removeFromCart(index);
        return;
    }
    
    cart[index].quantity = newQuantity;
    localStorage.setItem('tshirtk_cart', JSON.stringify(cart));
    updateCartCount();
    if (typeof displayCartItems === 'function') {
        displayCartItems();
    }
}

// عرض المنتجات المميزة في الصفحة الرئيسية
function displayFeaturedProducts() {
    const container = document.getElementById('featuredProducts');
    if (!container) return;

    const featuredProducts = products.slice(0, 4); // أول 4 منتجات
    
    container.innerHTML = featuredProducts.map(product => `
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card product-card h-100">
                <img src="${product.image}" class="card-img-top product-image" alt="${product.name}">
                <div class="card-body d-flex flex-column">
                    <h5 class="product-title">${product.name}</h5>
                    <p class="card-text text-muted flex-grow-1">${product.description}</p>
                    <div class="product-price">${product.price} ريال</div>
                    <button class="btn btn-add-cart w-100" onclick="showProductModal(${product.id})">
                        <i class="bi bi-cart-plus"></i> أضف للعربة
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// عرض جميع المنتجات في صفحة المنتجات
function displayAllProducts() {
    const container = document.getElementById('allProducts');
    if (!container) return;

    container.innerHTML = products.map(product => `
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card product-card h-100">
                <img src="${product.image}" class="card-img-top product-image" alt="${product.name}">
                <div class="card-body d-flex flex-column">
                    <h5 class="product-title">${product.name}</h5>
                    <p class="card-text text-muted flex-grow-1">${product.description}</p>
                    <div class="product-price">${product.price} ريال</div>
                    <button class="btn btn-add-cart w-100" onclick="showProductModal(${product.id})">
                        <i class="bi bi-cart-plus"></i> أضف للعربة
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// إظهار مودال تفاصيل المنتج
function showProductModal(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    const modalHtml = `
        <div class="modal fade" id="productModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${product.name}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <img src="${product.image}" class="img-fluid rounded" alt="${product.name}">
                            </div>
                            <div class="col-md-6">
                                <p class="text-muted">${product.description}</p>
                                <h4 class="text-primary">${product.price} ريال</h4>
                                
                                <div class="mb-3">
                                    <label class="form-label">اللون:</label>
                                    <select class="form-select" id="selectedColor">
                                        ${product.colors.map(color => `<option value="${color}">${color}</option>`).join('')}
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">المقاس:</label>
                                    <select class="form-select" id="selectedSize">
                                        ${product.sizes.map(size => `<option value="${size}">${size}</option>`).join('')}
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">الكمية:</label>
                                    <input type="number" class="form-control" id="selectedQuantity" value="1" min="1" max="10">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-primary" onclick="addProductFromModal(${product.id})">
                            <i class="bi bi-cart-plus"></i> أضف للعربة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إزالة المودال السابق إن وجد
    const existingModal = document.getElementById('productModal');
    if (existingModal) {
        existingModal.remove();
    }

    // إضافة المودال الجديد
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // إظهار المودال
    const modal = new bootstrap.Modal(document.getElementById('productModal'));
    modal.show();
}

// إضافة المنتج من المودال
function addProductFromModal(productId) {
    const selectedColor = document.getElementById('selectedColor').value;
    const selectedSize = document.getElementById('selectedSize').value;
    const selectedQuantity = parseInt(document.getElementById('selectedQuantity').value);

    addToCart(productId, selectedQuantity, selectedColor, selectedSize);
    
    // إغلاق المودال
    const modal = bootstrap.Modal.getInstance(document.getElementById('productModal'));
    modal.hide();
}

// إظهار الإشعارات
function showNotification(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', alertHtml);
    
    // إزالة الإشعار تلقائياً بعد 3 ثوان
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert');
        if (alerts.length > 0) {
            alerts[alerts.length - 1].remove();
        }
    }, 3000);
}

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    updateCartCount();
    
    // عرض المنتجات حسب الصفحة
    if (document.getElementById('featuredProducts')) {
        displayFeaturedProducts();
    }
    
    if (document.getElementById('allProducts')) {
        displayAllProducts();
    }
    
    // إضافة تأثير fade-in للعناصر
    const fadeElements = document.querySelectorAll('.fade-in');
    fadeElements.forEach((element, index) => {
        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 200);
    });
});
