// Tshirtk - JavaScript الرئيسي

// بيانات التيشيرتات المصرية
const products = [
    {
        id: 1,
        name: "تيشيرت قطن مصري رجالي كلاسيكي",
        price: 180,
        image: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop&crop=center",
        description: "تيشيرت قطن مصري 100% فاخر ومريح للاستخدام اليومي، مصنوع من أجود أنواع القطن المصري الأصلي",
        colors: ["أسود", "أبيض", "كحلي", "رمادي"],
        sizes: ["S", "M", "L", "XL", "XXL"],
        category: "رجالي",
        rating: 4.8,
        reviews: 245
    },
    {
        id: 2,
        name: "تيشيرت نسائي قطن مصري ناعم",
        price: 160,
        image: "https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=400&h=400&fit=crop&crop=center",
        description: "تيشيرت نسائي من القطن المصري الفاخر، بتصميم عصري ومريح مثالي للإطلالات اليومية",
        colors: ["وردي", "أبيض", "أسود", "بيج"],
        sizes: ["XS", "S", "M", "L", "XL"],
        category: "نسائي",
        rating: 4.9,
        reviews: 189
    },
    {
        id: 3,
        name: "تيشيرت رياضي مصري للجري",
        price: 220,
        image: "https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=400&h=400&fit=crop&crop=center",
        description: "تيشيرت رياضي مصنوع من قماش تقني مصري يمتص العرق ويوفر تهوية ممتازة للرياضيين",
        colors: ["أخضر", "أزرق", "أسود", "أحمر"],
        sizes: ["S", "M", "L", "XL"],
        category: "رياضي",
        rating: 4.7,
        reviews: 156
    },
    {
        id: 4,
        name: "تيشيرت بولو مصري فاخر",
        price: 280,
        image: "https://images.unsplash.com/photo-1586790170083-2f9ceadc732d?w=400&h=400&fit=crop&crop=center",
        description: "تيشيرت بولو من القطن المصري الفاخر، مناسب للمناسبات الرسمية والعمل بتصميم أنيق",
        colors: ["كحلي", "أبيض", "رمادي", "بني"],
        sizes: ["S", "M", "L", "XL", "XXL"],
        category: "رجالي",
        rating: 4.6,
        reviews: 134
    },
    {
        id: 5,
        name: "تيشيرت مصري بطبعة فرعونية",
        price: 200,
        image: "https://images.unsplash.com/photo-1583743814966-8936f37f4678?w=400&h=400&fit=crop&crop=center",
        description: "تيشيرت بطبعة فرعونية مميزة وألوان زاهية، يعكس التراث المصري العريق بتصميم عصري",
        colors: ["ذهبي", "أبيض", "أسود"],
        sizes: ["S", "M", "L", "XL"],
        category: "تراثي",
        rating: 4.5,
        reviews: 98
    },
    {
        id: 6,
        name: "تيشيرت أطفال قطن مصري",
        price: 120,
        image: "https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=400&h=400&fit=crop&crop=center",
        description: "تيشيرت أطفال من القطن المصري الناعم، بألوان مرحة وتصميم جذاب مناسب للأطفال",
        colors: ["برتقالي", "أزرق", "أخضر", "وردي"],
        sizes: ["XS", "S", "M", "L"],
        category: "أطفال",
        rating: 4.8,
        reviews: 167
    },
    {
        id: 7,
        name: "تيشيرت شتوي مصري بكابوشون",
        price: 320,
        image: "https://images.unsplash.com/photo-1556821840-3a63f95609a7?w=400&h=400&fit=crop&crop=center",
        description: "تيشيرت شتوي بكابوشون من القطن المصري السميك، دافئ ومريح مثالي للطقس البارد",
        colors: ["أسود", "رمادي", "كحلي", "بني"],
        sizes: ["S", "M", "L", "XL", "XXL"],
        category: "شتوي",
        rating: 4.7,
        reviews: 203
    },
    {
        id: 8,
        name: "تيشيرت نسائي رياضي مصري",
        price: 190,
        image: "https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=400&h=400&fit=crop&crop=center",
        description: "تيشيرت رياضي نسائي من القطن المصري المخلوط، مصمم خصيصاً للتمارين الرياضية واللياقة البدنية",
        colors: ["وردي", "أسود", "أبيض", "بنفسجي"],
        sizes: ["XS", "S", "M", "L", "XL"],
        category: "رياضي",
        rating: 4.6,
        reviews: 142
    },
    {
        id: 9,
        name: "تيشيرت مصري بطبعة القاهرة",
        price: 210,
        image: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop&crop=center",
        description: "تيشيرت بطبعة معالم القاهرة الشهيرة، من القطن المصري الأصلي بتصميم يعكس جمال العاصمة",
        colors: ["أسود", "أبيض", "كحلي"],
        sizes: ["S", "M", "L", "XL", "XXL"],
        category: "تراثي",
        rating: 4.4,
        reviews: 87
    },
    {
        id: 10,
        name: "تيشيرت مصري عادي يومي",
        price: 140,
        image: "https://images.unsplash.com/photo-1583743814966-8936f37f4678?w=400&h=400&fit=crop&crop=center",
        description: "تيشيرت يومي بسيط من القطن المصري، مريح وعملي للاستخدام اليومي بسعر اقتصادي",
        colors: ["أبيض", "أسود", "رمادي", "كحلي"],
        sizes: ["S", "M", "L", "XL"],
        category: "يومي",
        rating: 4.3,
        reviews: 312
    }
];

// إدارة العربة
let cart = JSON.parse(localStorage.getItem('tshirtk_cart')) || [];

// تحديث عداد العربة
function updateCartCount() {
    const cartCount = document.getElementById('cartCount');
    if (cartCount) {
        const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
        cartCount.textContent = totalItems;
        cartCount.style.display = totalItems > 0 ? 'flex' : 'none';
    }
}

// إضافة منتج للعربة
function addToCart(productId, quantity = 1, selectedColor = null, selectedSize = null) {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    const existingItem = cart.find(item => 
        item.id === productId && 
        item.color === selectedColor && 
        item.size === selectedSize
    );

    if (existingItem) {
        existingItem.quantity += quantity;
    } else {
        cart.push({
            id: productId,
            name: product.name,
            price: product.price,
            image: product.image,
            quantity: quantity,
            color: selectedColor || product.colors[0],
            size: selectedSize || product.sizes[0]
        });
    }

    localStorage.setItem('tshirtk_cart', JSON.stringify(cart));
    updateCartCount();
    
    // إظهار رسالة نجاح
    showNotification('تم إضافة المنتج إلى العربة بنجاح!', 'success');
}

// حذف منتج من العربة
function removeFromCart(index) {
    cart.splice(index, 1);
    localStorage.setItem('tshirtk_cart', JSON.stringify(cart));
    updateCartCount();
    if (typeof displayCartItems === 'function') {
        displayCartItems();
    }
}

// تحديث كمية المنتج
function updateQuantity(index, newQuantity) {
    if (newQuantity <= 0) {
        removeFromCart(index);
        return;
    }
    
    cart[index].quantity = newQuantity;
    localStorage.setItem('tshirtk_cart', JSON.stringify(cart));
    updateCartCount();
    if (typeof displayCartItems === 'function') {
        displayCartItems();
    }
}

// عرض المنتجات المميزة في الصفحة الرئيسية
function displayFeaturedProducts() {
    const container = document.getElementById('featuredProducts');
    if (!container) return;

    const featuredProducts = products.slice(0, 4); // أول 4 منتجات

    container.innerHTML = featuredProducts.map(product => `
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card product-card h-100">
                <div class="position-relative">
                    <img src="${product.image}" class="card-img-top product-image" alt="${product.name}">
                    <div class="product-badge">
                        <span class="badge bg-primary">${product.category}</span>
                    </div>
                </div>
                <div class="card-body d-flex flex-column">
                    <h5 class="product-title">${product.name}</h5>
                    <div class="product-rating mb-2">
                        ${generateStars(product.rating)}
                        <small class="text-muted">(${product.reviews} تقييم)</small>
                    </div>
                    <p class="card-text text-muted flex-grow-1">${product.description.substring(0, 80)}...</p>
                    <div class="product-price">${product.price} ريال</div>
                    <button class="btn btn-add-cart w-100 mt-2" onclick="showProductModal(${product.id})">
                        <i class="bi bi-cart-plus"></i> أضف للعربة
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// عرض جميع المنتجات في صفحة المنتجات
function displayAllProducts() {
    const container = document.getElementById('allProducts');
    if (!container) return;

    container.innerHTML = products.map(product => `
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card product-card h-100">
                <div class="position-relative">
                    <img src="${product.image}" class="card-img-top product-image" alt="${product.name}">
                    <div class="product-badge">
                        <span class="badge bg-primary">${product.category}</span>
                    </div>
                    ${product.rating >= 4.5 ? '<div class="product-bestseller"><span class="badge bg-warning">الأكثر مبيعاً</span></div>' : ''}
                </div>
                <div class="card-body d-flex flex-column">
                    <h5 class="product-title">${product.name}</h5>
                    <div class="product-rating mb-2">
                        ${generateStars(product.rating)}
                        <small class="text-muted">(${product.reviews} تقييم)</small>
                    </div>
                    <p class="card-text text-muted flex-grow-1">${product.description.substring(0, 100)}...</p>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div class="product-price">${product.price} ريال</div>
                        <small class="text-muted">${product.colors.length} ألوان</small>
                    </div>
                    <button class="btn btn-add-cart w-100" onclick="showProductModal(${product.id})">
                        <i class="bi bi-cart-plus"></i> أضف للعربة
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// دالة لتوليد النجوم للتقييم
function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    let starsHtml = '';

    for (let i = 0; i < fullStars; i++) {
        starsHtml += '<i class="bi bi-star-fill text-warning"></i>';
    }

    if (hasHalfStar) {
        starsHtml += '<i class="bi bi-star-half text-warning"></i>';
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
        starsHtml += '<i class="bi bi-star text-warning"></i>';
    }

    return starsHtml + ` <span class="text-muted">${rating}</span>`;
}

// إظهار مودال تفاصيل المنتج
function showProductModal(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    const modalHtml = `
        <div class="modal fade" id="productModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${product.name}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <img src="${product.image}" class="img-fluid rounded" alt="${product.name}">
                            </div>
                            <div class="col-md-6">
                                <p class="text-muted">${product.description}</p>
                                <h4 class="text-primary">${product.price} ريال</h4>
                                
                                <div class="mb-3">
                                    <label class="form-label">اللون:</label>
                                    <select class="form-select" id="selectedColor">
                                        ${product.colors.map(color => `<option value="${color}">${color}</option>`).join('')}
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">المقاس:</label>
                                    <select class="form-select" id="selectedSize">
                                        ${product.sizes.map(size => `<option value="${size}">${size}</option>`).join('')}
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">الكمية:</label>
                                    <input type="number" class="form-control" id="selectedQuantity" value="1" min="1" max="10">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-primary" onclick="addProductFromModal(${product.id})">
                            <i class="bi bi-cart-plus"></i> أضف للعربة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إزالة المودال السابق إن وجد
    const existingModal = document.getElementById('productModal');
    if (existingModal) {
        existingModal.remove();
    }

    // إضافة المودال الجديد
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // إظهار المودال
    const modal = new bootstrap.Modal(document.getElementById('productModal'));
    modal.show();
}

// إضافة المنتج من المودال
function addProductFromModal(productId) {
    const selectedColor = document.getElementById('selectedColor').value;
    const selectedSize = document.getElementById('selectedSize').value;
    const selectedQuantity = parseInt(document.getElementById('selectedQuantity').value);

    addToCart(productId, selectedQuantity, selectedColor, selectedSize);
    
    // إغلاق المودال
    const modal = bootstrap.Modal.getInstance(document.getElementById('productModal'));
    modal.hide();
}

// إظهار الإشعارات
function showNotification(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', alertHtml);
    
    // إزالة الإشعار تلقائياً بعد 3 ثوان
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert');
        if (alerts.length > 0) {
            alerts[alerts.length - 1].remove();
        }
    }, 3000);
}

// تحريك الأرقام في قسم الإحصائيات
function animateNumbers() {
    const statNumbers = document.querySelectorAll('.stat-number');

    statNumbers.forEach(stat => {
        const target = parseFloat(stat.getAttribute('data-target'));
        const increment = target / 100;
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }

            // تنسيق الرقم حسب النوع
            if (target === 4.8) {
                stat.textContent = current.toFixed(1);
            } else if (target >= 1000) {
                stat.textContent = Math.floor(current).toLocaleString('ar-SA') + '+';
            } else {
                stat.textContent = Math.floor(current);
            }
        }, 20);
    });
}

// مراقب التقاطع لتحريك الأرقام عند الوصول للقسم
function setupIntersectionObserver() {
    const statsSection = document.querySelector('.stats-section');
    if (!statsSection) return;

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateNumbers();
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    observer.observe(statsSection);
}

// تحسين تأثير fade-in
function setupFadeInAnimation() {
    const fadeElements = document.querySelectorAll('.fade-in');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('show');
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });

    fadeElements.forEach(element => {
        observer.observe(element);
    });
}

// إخفاء شاشة التحميل
function hideLoadingOverlay() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        setTimeout(() => {
            loadingOverlay.classList.add('hidden');
            setTimeout(() => {
                loadingOverlay.remove();
            }, 500);
        }, 1000);
    }
}

// تحسين تجربة المستخدم
function enhanceUserExperience() {
    // إضافة تأثير ripple للأزرار
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    updateCartCount();

    // عرض المنتجات حسب الصفحة
    if (document.getElementById('featuredProducts')) {
        displayFeaturedProducts();
    }

    if (document.getElementById('allProducts')) {
        displayAllProducts();
    }

    // تهيئة التأثيرات
    setupFadeInAnimation();
    setupIntersectionObserver();
    enhanceUserExperience();

    // إخفاء شاشة التحميل
    hideLoadingOverlay();

    // تحسين التنقل السلس
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});
