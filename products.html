<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المنتجات - Tshirtk</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="bi bi-shop"></i> Tshirtk
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="products.html">المنتجات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">تواصل معنا</a>
                    </li>
                </ul>
                
                <div class="d-flex">
                    <a href="cart.html" class="btn btn-outline-primary me-2">
                        <div class="cart-icon">
                            <i class="bi bi-cart3"></i>
                            <span class="cart-badge" id="cartCount">0</span>
                        </div>
                        العربة
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="bg-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="display-4 fw-bold text-primary">جميع المنتجات</h1>
                    <p class="lead text-muted">اكتشف مجموعتنا الكاملة من التيشيرتات العصرية</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section class="py-5">
        <div class="container">
            <!-- Filter Section -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-funnel"></i> تصفية المنتجات
                            </h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">السعر:</label>
                                    <select class="form-select" id="priceFilter">
                                        <option value="">جميع الأسعار</option>
                                        <option value="0-75">أقل من 75 ريال</option>
                                        <option value="75-100">75 - 100 ريال</option>
                                        <option value="100-150">100 - 150 ريال</option>
                                        <option value="150+">أكثر من 150 ريال</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">النوع:</label>
                                    <select class="form-select" id="typeFilter">
                                        <option value="">جميع الأنواع</option>
                                        <option value="رجالي">رجالي</option>
                                        <option value="نسائي">نسائي</option>
                                        <option value="رياضي">رياضي</option>
                                        <option value="أطفال">أطفال</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">الترتيب:</label>
                                    <select class="form-select" id="sortFilter">
                                        <option value="">الترتيب الافتراضي</option>
                                        <option value="price-low">السعر: من الأقل للأعلى</option>
                                        <option value="price-high">السعر: من الأعلى للأقل</option>
                                        <option value="name">الاسم: أ - ي</option>
                                    </select>
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    <button class="btn btn-primary w-100" onclick="applyFilters()">
                                        <i class="bi bi-search"></i> تطبيق التصفية
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="row" id="allProducts">
                <!-- سيتم إضافة المنتجات هنا بواسطة JavaScript -->
            </div>

            <!-- No Results Message -->
            <div id="noResults" class="text-center py-5" style="display: none;">
                <i class="bi bi-search display-1 text-muted"></i>
                <h3 class="mt-3">لا توجد منتجات مطابقة</h3>
                <p class="text-muted">جرب تغيير معايير البحث أو التصفية</p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4">
                    <h5><i class="bi bi-shop"></i> Tshirtk</h5>
                    <p>متجرك الأول لأفضل التيشيرتات العصرية والمريحة. نقدم لك أحدث التصاميم بأعلى جودة وأفضل الأسعار.</p>
                </div>
                <div class="col-md-4 mb-4">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="cart.html">العربة</a></li>
                        <li><a href="contact.html">تواصل معنا</a></li>
                    </ul>
                </div>
                <div class="col-md-4 mb-4">
                    <h5>تواصل معنا</h5>
                    <p><i class="bi bi-telephone"></i> +966 50 123 4567</p>
                    <p><i class="bi bi-envelope"></i> <EMAIL></p>
                    <div class="social-icons">
                        <a href="#"><i class="bi bi-facebook"></i></a>
                        <a href="#"><i class="bi bi-twitter"></i></a>
                        <a href="#"><i class="bi bi-instagram"></i></a>
                        <a href="#"><i class="bi bi-whatsapp"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p>&copy; 2024 Tshirtk. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/main.js"></script>
    <script>
        // تطبيق التصفية
        function applyFilters() {
            const priceFilter = document.getElementById('priceFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            const sortFilter = document.getElementById('sortFilter').value;
            
            let filteredProducts = [...products];
            
            // تصفية حسب السعر
            if (priceFilter) {
                if (priceFilter === '0-75') {
                    filteredProducts = filteredProducts.filter(p => p.price < 75);
                } else if (priceFilter === '75-100') {
                    filteredProducts = filteredProducts.filter(p => p.price >= 75 && p.price <= 100);
                } else if (priceFilter === '100-150') {
                    filteredProducts = filteredProducts.filter(p => p.price > 100 && p.price <= 150);
                } else if (priceFilter === '150+') {
                    filteredProducts = filteredProducts.filter(p => p.price > 150);
                }
            }
            
            // تصفية حسب النوع
            if (typeFilter) {
                filteredProducts = filteredProducts.filter(p => p.name.includes(typeFilter));
            }
            
            // ترتيب المنتجات
            if (sortFilter === 'price-low') {
                filteredProducts.sort((a, b) => a.price - b.price);
            } else if (sortFilter === 'price-high') {
                filteredProducts.sort((a, b) => b.price - a.price);
            } else if (sortFilter === 'name') {
                filteredProducts.sort((a, b) => a.name.localeCompare(b.name, 'ar'));
            }
            
            displayFilteredProducts(filteredProducts);
        }
        
        // عرض المنتجات المفلترة
        function displayFilteredProducts(filteredProducts) {
            const container = document.getElementById('allProducts');
            const noResults = document.getElementById('noResults');
            
            if (filteredProducts.length === 0) {
                container.innerHTML = '';
                noResults.style.display = 'block';
                return;
            }
            
            noResults.style.display = 'none';
            container.innerHTML = filteredProducts.map(product => `
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card product-card h-100">
                        <img src="${product.image}" class="card-img-top product-image" alt="${product.name}">
                        <div class="card-body d-flex flex-column">
                            <h5 class="product-title">${product.name}</h5>
                            <p class="card-text text-muted flex-grow-1">${product.description}</p>
                            <div class="product-price">${product.price} ريال</div>
                            <button class="btn btn-add-cart w-100" onclick="showProductModal(${product.id})">
                                <i class="bi bi-cart-plus"></i> أضف للعربة
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }
    </script>
</body>
</html>
