*{
    font-family: Verdana, Geneva, Tahoma, sans-serif;
}
body{
    margin: 0;
    padding: 0;
    background: #ffffff;
    direction: rtl;
}
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgb(0, 0, 0);
    backdrop-filter: blur(50px);
    -webkit-backdrop-filter: blur(50px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.8);
}

.navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:linear-gradient(200deg, rgb(18, 38, 141) 0% 0%, rgb(245, 124, 4) 100%);
    z-index: -1;
    backdrop-filter: blur(60px);
    -webkit-backdrop-filter: blur(60px);
}

.navbar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgb(10, 10, 10);
    z-index: -2;
    opacity: 0.95;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 10;
}

.logo {
    display: flex;
    align-items: center;
    position: relative;
}

.logo-text {
    font-family: 'Cairo', sans-serif;
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(45deg, #e2e5e6, #f6f5f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-orbit {
    width: 30px;
    height: 30px;
    border: 2px solid #00d4ff;
    border-radius: 50%;
    margin-right: 10px;
    position: relative;
    animation: orbit 3s linear infinite;
}

.logo-orbit::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    background: #ff00ff;
    border-radius: 50%;
    box-shadow: 0 0 10px #ff00ff;
}

@keyframes orbit {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    font-weight: 400;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #00d4ff;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    right: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(45deg, #00d4ff, #ff00ff);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}
/* قسم Hero */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: white;
    padding: 120px 0;
    min-height: 600px;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: bold;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
}

.hero-title {
    font-size: 3.8rem;
    font-weight: bold;
    margin-bottom: 25px;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.4rem;
    margin-bottom: 35px;
    opacity: 0.95;
    line-height: 1.6;
}

.hero-features {
    margin: 30px 0;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-size: 1.1rem;
}

.feature-item i {
    margin-left: 12px;
    font-size: 1.2rem;
}

.hero-buttons {
    margin-top: 40px;
}

.btn-hero {
    background-color: var(--secondary-color);
    border: none;
    padding: 18px 45px;
    font-size: 1.3rem;
    font-weight: bold;
    border-radius: 50px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.btn-hero:hover {
    background-color: #d35400;
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.3);
}

.btn-outline-light {
    border: 2px solid rgba(255,255,255,0.8);
    color: white;
    padding: 18px 35px;
    font-size: 1.1rem;
    font-weight: bold;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    background-color: white;
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* صورة Hero */
.hero-image-container {
    position: relative;
    display: inline-block;
}

.hero-image {
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    transition: transform 0.3s ease;
}

.hero-image:hover {
    transform: scale(1.02);
}

.hero-image-overlay {
    position: absolute;
    bottom: 20px;
    right: 20px;
}
