<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>العربة - Tshirtk</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="bi bi-shop"></i> Tshirtk
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.html">المنتجات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">تواصل معنا</a>
                    </li>
                </ul>
                
                <div class="d-flex">
                    <a href="cart.html" class="btn btn-primary me-2">
                        <div class="cart-icon">
                            <i class="bi bi-cart3"></i>
                            <span class="cart-badge" id="cartCount">0</span>
                        </div>
                        العربة
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="bg-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="display-4 fw-bold text-primary">
                        <i class="bi bi-cart3"></i> عربة التسوق
                    </h1>
                    <p class="lead text-muted">راجع منتجاتك المختارة</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Cart Content -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <!-- Cart Items -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-bag"></i> المنتجات المختارة
                            </h5>
                        </div>
                        <div class="card-body" id="cartItems">
                            <!-- سيتم إضافة عناصر العربة هنا -->
                        </div>
                    </div>
                </div>

                <!-- Cart Summary -->
                <div class="col-lg-4">
                    <div class="card cart-total">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-calculator"></i> ملخص الطلب
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-3">
                                <span>المجموع الفرعي:</span>
                                <span id="subtotal">0 ريال</span>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span>الشحن:</span>
                                <span id="shipping">مجاني</span>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span>الضريبة (15%):</span>
                                <span id="tax">0 ريال</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-4">
                                <strong>المجموع الكلي:</strong>
                                <strong id="total" class="text-primary">0 ريال</strong>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <a href="checkout.html" class="btn btn-primary btn-lg" id="checkoutBtn">
                                    <i class="bi bi-credit-card"></i> إتمام الشراء
                                </a>
                                <a href="products.html" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-right"></i> متابعة التسوق
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Promo Code -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-tag"></i> كود الخصم
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="أدخل كود الخصم" id="promoCode">
                                <button class="btn btn-outline-primary" onclick="applyPromoCode()">تطبيق</button>
                            </div>
                            <small class="text-muted mt-2 d-block">
                                💡 جرب: <code>TSHIRTK10</code> للحصول على خصم 10%
                            </small>
                        </div>
                    </div>

                    <!-- Free Shipping Alert -->
                    <div class="alert alert-info mt-4" id="shippingAlert">
                        <i class="bi bi-truck"></i>
                        <strong>توصيل مجاني!</strong>
                        أضف منتجات بقيمة <span id="remainingAmount">0</span> ريال أخرى للحصول على توصيل مجاني
                    </div>

                    <!-- Trust Badges -->
                    <div class="trust-badges mt-4">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="trust-item">
                                    <i class="bi bi-shield-check text-success"></i>
                                    <small>دفع آمن</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="trust-item">
                                    <i class="bi bi-arrow-repeat text-primary"></i>
                                    <small>إرجاع سهل</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="trust-item">
                                    <i class="bi bi-headset text-warning"></i>
                                    <small>دعم 24/7</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Empty Cart Message -->
            <div id="emptyCart" class="text-center py-5" style="display: none;">
                <i class="bi bi-cart-x display-1 text-muted"></i>
                <h3 class="mt-3">العربة فارغة</h3>
                <p class="text-muted">لم تقم بإضافة أي منتجات إلى العربة بعد</p>
                <a href="products.html" class="btn btn-primary btn-lg">
                    <i class="bi bi-shop"></i> ابدأ التسوق
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4">
                    <h5><i class="bi bi-shop"></i> Tshirtk</h5>
                    <p>متجرك الأول لأفضل التيشيرتات العصرية والمريحة. نقدم لك أحدث التصاميم بأعلى جودة وأفضل الأسعار.</p>
                </div>
                <div class="col-md-4 mb-4">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="cart.html">العربة</a></li>
                        <li><a href="contact.html">تواصل معنا</a></li>
                    </ul>
                </div>
                <div class="col-md-4 mb-4">
                    <h5>تواصل معنا</h5>
                    <p><i class="bi bi-telephone"></i> +966 50 123 4567</p>
                    <p><i class="bi bi-envelope"></i> <EMAIL></p>
                    <div class="social-icons">
                        <a href="#"><i class="bi bi-facebook"></i></a>
                        <a href="#"><i class="bi bi-twitter"></i></a>
                        <a href="#"><i class="bi bi-instagram"></i></a>
                        <a href="#"><i class="bi bi-whatsapp"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p>&copy; 2024 Tshirtk. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/main.js"></script>
    <script>
        // عرض عناصر العربة
        function displayCartItems() {
            const cartItemsContainer = document.getElementById('cartItems');
            const emptyCartMessage = document.getElementById('emptyCart');
            const checkoutBtn = document.getElementById('checkoutBtn');

            if (cart.length === 0) {
                cartItemsContainer.innerHTML = '';
                emptyCartMessage.style.display = 'block';
                checkoutBtn.style.display = 'none';
                updateCartSummary();
                return;
            }

            emptyCartMessage.style.display = 'none';
            checkoutBtn.style.display = 'block';

            cartItemsContainer.innerHTML = cart.map((item, index) => `
                <div class="cart-item">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            <img src="${item.image}" class="img-fluid rounded" alt="${item.name}">
                        </div>
                        <div class="col-md-4">
                            <h6 class="mb-1">${item.name}</h6>
                            <small class="text-muted">اللون: ${item.color} | المقاس: ${item.size}</small>
                        </div>
                        <div class="col-md-2">
                            <div class="input-group input-group-sm">
                                <button class="btn btn-outline-secondary" onclick="updateQuantity(${index}, ${item.quantity - 1})">-</button>
                                <input type="text" class="form-control text-center" value="${item.quantity}" readonly>
                                <button class="btn btn-outline-secondary" onclick="updateQuantity(${index}, ${item.quantity + 1})">+</button>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <strong>${item.price} ريال</strong>
                        </div>
                        <div class="col-md-2 text-end">
                            <button class="btn btn-outline-danger btn-sm" onclick="removeFromCart(${index})">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');

            updateCartSummary();
        }

        // تحديث ملخص العربة
        function updateCartSummary() {
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const tax = subtotal * 0.15; // ضريبة 15%
            const total = subtotal + tax;

            document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ريال';
            document.getElementById('tax').textContent = tax.toFixed(2) + ' ريال';
            document.getElementById('total').textContent = total.toFixed(2) + ' ريال';

            // تحديث تنبيه الشحن المجاني
            updateShippingAlert(subtotal);
        }

        // تحديث تنبيه الشحن المجاني
        function updateShippingAlert(subtotal) {
            const shippingAlert = document.getElementById('shippingAlert');
            const remainingAmount = document.getElementById('remainingAmount');
            const freeShippingThreshold = 200;

            if (subtotal >= freeShippingThreshold) {
                shippingAlert.className = 'alert alert-success mt-4';
                shippingAlert.innerHTML = '<i class="bi bi-check-circle"></i> <strong>مبروك!</strong> حصلت على توصيل مجاني';
            } else {
                const remaining = freeShippingThreshold - subtotal;
                remainingAmount.textContent = remaining.toFixed(2);
                shippingAlert.className = 'alert alert-info mt-4';
            }
        }

        // تطبيق كود الخصم
        function applyPromoCode() {
            const promoCode = document.getElementById('promoCode').value.trim();
            
            if (promoCode === 'TSHIRTK10') {
                showNotification('تم تطبيق كود الخصم! خصم 10%', 'success');
                // يمكن إضافة منطق الخصم هنا
            } else if (promoCode === '') {
                showNotification('يرجى إدخال كود الخصم', 'warning');
            } else {
                showNotification('كود الخصم غير صحيح', 'danger');
            }
        }

        // تهيئة صفحة العربة
        document.addEventListener('DOMContentLoaded', function() {
            displayCartItems();
        });
    </script>
</body>
</html>
