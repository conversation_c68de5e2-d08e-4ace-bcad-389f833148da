<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إتمام الشراء - Tshirtk</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="bi bi-shop"></i> Tshirtk
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.html">المنتجات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">تواصل معنا</a>
                    </li>
                </ul>
                
                <div class="d-flex">
                    <a href="cart.html" class="btn btn-outline-primary me-2">
                        <div class="cart-icon">
                            <i class="bi bi-cart3"></i>
                            <span class="cart-badge" id="cartCount">0</span>
                        </div>
                        العربة
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="bg-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="display-4 fw-bold text-primary">
                        <i class="bi bi-credit-card"></i> إتمام الشراء
                    </h1>
                    <p class="lead text-muted">أكمل بياناتك لإتمام عملية الشراء</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Checkout Form -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <!-- Customer Information -->
                <div class="col-lg-8">
                    <div class="checkout-form">
                        <h4 class="mb-4">
                            <i class="bi bi-person-fill"></i> بيانات العميل
                        </h4>
                        
                        <form id="checkoutForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="firstName" class="form-label">الاسم الأول *</label>
                                    <input type="text" class="form-control" id="firstName" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="lastName" class="form-label">اسم العائلة *</label>
                                    <input type="text" class="form-control" id="lastName" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني *</label>
                                    <input type="email" class="form-control" id="email" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف *</label>
                                    <input type="tel" class="form-control" id="phone" required>
                                </div>
                            </div>

                            <h5 class="mt-4 mb-3">
                                <i class="bi bi-geo-alt-fill"></i> عنوان التوصيل
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="city" class="form-label">المدينة *</label>
                                    <select class="form-select" id="city" required>
                                        <option value="">اختر المدينة</option>
                                        <option value="الرياض">الرياض</option>
                                        <option value="جدة">جدة</option>
                                        <option value="الدمام">الدمام</option>
                                        <option value="مكة">مكة المكرمة</option>
                                        <option value="المدينة">المدينة المنورة</option>
                                        <option value="الطائف">الطائف</option>
                                        <option value="تبوك">تبوك</option>
                                        <option value="أخرى">أخرى</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="district" class="form-label">الحي *</label>
                                    <input type="text" class="form-control" id="district" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">العنوان التفصيلي *</label>
                                <textarea class="form-control" id="address" rows="3" placeholder="اسم الشارع، رقم المبنى، رقم الشقة..." required></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="postalCode" class="form-label">الرمز البريدي</label>
                                <input type="text" class="form-control" id="postalCode">
                            </div>

                            <h5 class="mt-4 mb-3">
                                <i class="bi bi-credit-card-fill"></i> طريقة الدفع
                            </h5>
                            
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="radio" name="paymentMethod" id="cashOnDelivery" value="cash" checked>
                                        <label class="form-check-label" for="cashOnDelivery">
                                            <i class="bi bi-cash"></i> الدفع عند الاستلام
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="radio" name="paymentMethod" id="bankTransfer" value="bank">
                                        <label class="form-check-label" for="bankTransfer">
                                            <i class="bi bi-bank"></i> تحويل بنكي
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="radio" name="paymentMethod" id="creditCard" value="card">
                                        <label class="form-check-label" for="creditCard">
                                            <i class="bi bi-credit-card"></i> بطاقة ائتمانية
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات إضافية</label>
                                <textarea class="form-control" id="notes" rows="3" placeholder="أي ملاحظات خاصة بالطلب..."></textarea>
                            </div>

                            <div class="form-check mb-4">
                                <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                                <label class="form-check-label" for="agreeTerms">
                                    أوافق على <a href="#" class="text-primary">الشروط والأحكام</a> و <a href="#" class="text-primary">سياسة الخصوصية</a> *
                                </label>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="col-lg-4">
                    <div class="card cart-total">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-receipt"></i> ملخص الطلب
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="orderItems">
                                <!-- سيتم إضافة عناصر الطلب هنا -->
                            </div>
                            
                            <hr>
                            
                            <div class="d-flex justify-content-between mb-2">
                                <span>المجموع الفرعي:</span>
                                <span id="orderSubtotal">0 ريال</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>الشحن:</span>
                                <span id="orderShipping">مجاني</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>الضريبة (15%):</span>
                                <span id="orderTax">0 ريال</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-4">
                                <strong>المجموع الكلي:</strong>
                                <strong id="orderTotal" class="text-primary">0 ريال</strong>
                            </div>
                            
                            <div class="d-grid">
                                <button type="button" class="btn btn-primary btn-lg" onclick="submitOrder()">
                                    <i class="bi bi-check-circle"></i> تأكيد الطلب
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4">
                    <h5><i class="bi bi-shop"></i> Tshirtk</h5>
                    <p>متجرك الأول لأفضل التيشيرتات العصرية والمريحة. نقدم لك أحدث التصاميم بأعلى جودة وأفضل الأسعار.</p>
                </div>
                <div class="col-md-4 mb-4">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="products.html">المنتجات</a></li>
                        <li><a href="cart.html">العربة</a></li>
                        <li><a href="contact.html">تواصل معنا</a></li>
                    </ul>
                </div>
                <div class="col-md-4 mb-4">
                    <h5>تواصل معنا</h5>
                    <p><i class="bi bi-telephone"></i> +966 50 123 4567</p>
                    <p><i class="bi bi-envelope"></i> <EMAIL></p>
                    <div class="social-icons">
                        <a href="#"><i class="bi bi-facebook"></i></a>
                        <a href="#"><i class="bi bi-twitter"></i></a>
                        <a href="#"><i class="bi bi-instagram"></i></a>
                        <a href="#"><i class="bi bi-whatsapp"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p>&copy; 2024 Tshirtk. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/main.js"></script>
    <script>
        // عرض ملخص الطلب
        function displayOrderSummary() {
            const orderItemsContainer = document.getElementById('orderItems');
            
            if (cart.length === 0) {
                window.location.href = 'cart.html';
                return;
            }

            orderItemsContainer.innerHTML = cart.map(item => `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <small class="text-muted">${item.name}</small><br>
                        <small class="text-muted">${item.color} - ${item.size} × ${item.quantity}</small>
                    </div>
                    <span>${(item.price * item.quantity).toFixed(2)} ريال</span>
                </div>
            `).join('');

            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const tax = subtotal * 0.15;
            const total = subtotal + tax;

            document.getElementById('orderSubtotal').textContent = subtotal.toFixed(2) + ' ريال';
            document.getElementById('orderTax').textContent = tax.toFixed(2) + ' ريال';
            document.getElementById('orderTotal').textContent = total.toFixed(2) + ' ريال';
        }

        // تأكيد الطلب
        function submitOrder() {
            const form = document.getElementById('checkoutForm');
            
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // جمع بيانات النموذج
            const orderData = {
                customer: {
                    firstName: document.getElementById('firstName').value,
                    lastName: document.getElementById('lastName').value,
                    email: document.getElementById('email').value,
                    phone: document.getElementById('phone').value
                },
                address: {
                    city: document.getElementById('city').value,
                    district: document.getElementById('district').value,
                    address: document.getElementById('address').value,
                    postalCode: document.getElementById('postalCode').value
                },
                paymentMethod: document.querySelector('input[name="paymentMethod"]:checked').value,
                notes: document.getElementById('notes').value,
                items: cart,
                timestamp: new Date().toISOString()
            };

            // حفظ الطلب في localStorage (في التطبيق الحقيقي سيتم إرساله للخادم)
            const orders = JSON.parse(localStorage.getItem('tshirtk_orders')) || [];
            orders.push(orderData);
            localStorage.setItem('tshirtk_orders', JSON.stringify(orders));

            // مسح العربة
            cart = [];
            localStorage.removeItem('tshirtk_cart');
            updateCartCount();

            // إظهار رسالة نجاح
            showNotification('تم تأكيد طلبك بنجاح! سنتواصل معك قريباً', 'success');
            
            // إعادة توجيه للصفحة الرئيسية بعد 3 ثوان
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 3000);
        }

        // تهيئة صفحة الدفع
        document.addEventListener('DOMContentLoaded', function() {
            displayOrderSummary();
        });
    </script>
</body>
</html>
