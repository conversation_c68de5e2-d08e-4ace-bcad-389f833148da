# Tshirtk - موقع بيع التيشيرتات

موقع عربي احترافي لبيع التيشيرتات أونلاين مصمم باستخدام HTML وCSS وBootstrap فقط.

## 🌟 المميزات

- **تصميم متجاوب**: يعمل بشكل مثالي على جميع الأجهزة (الكمبيوتر، التابلت، الهاتف المحمول)
- **واجهة عربية**: تصميم RTL كامل باللغة العربية
- **سلة تسوق**: نظام عربة تسوق بسيط باستخدام localStorage
- **تصميم احترافي**: استخدام Bootstrap 5 مع تخصيصات CSS احترافية
- **تفاعلية**: تأثيرات هوفر وانتقالات سلسة
- **أيقونات**: استخدام Bootstrap Icons

## 📁 بنية الملفات

```
Tshirtk/
├── index.html          # الصفحة الرئيسية
├── products.html       # صفحة المنتجات
├── cart.html          # صفحة العربة
├── checkout.html      # صفحة الدفع
├── contact.html       # صفحة تواصل معنا
├── css/
│   └── style.css      # ملف التنسيقات المخصص
├── js/
│   └── main.js        # ملف JavaScript الرئيسي
└── README.md          # هذا الملف
```

## 🚀 كيفية الاستخدام

1. **تشغيل الموقع**:
   - افتح ملف `index.html` في المتصفح
   - أو استخدم خادم محلي مثل Live Server في VS Code

2. **التنقل**:
   - **الصفحة الرئيسية**: عرض المنتجات المميزة والمعلومات الأساسية
   - **المنتجات**: عرض جميع المنتجات مع إمكانية التصفية
   - **العربة**: عرض المنتجات المختارة وإدارتها
   - **الدفع**: نموذج إتمام الشراء
   - **تواصل معنا**: معلومات التواصل ونموذج المراسلة

## 🛍️ وظائف العربة

- **إضافة المنتجات**: اضغط على "أضف للعربة" لإضافة منتج
- **اختيار المواصفات**: اختر اللون والمقاس والكمية من النافذة المنبثقة
- **إدارة العربة**: تعديل الكمية أو حذف المنتجات
- **حفظ البيانات**: تُحفظ العربة في localStorage

## 🎨 التخصيص

### الألوان
يمكنك تغيير الألوان من ملف `css/style.css`:

```css
:root {
    --primary-color: #2c3e50;    /* اللون الأساسي */
    --secondary-color: #e67e22;  /* اللون الثانوي */
    --accent-color: #3498db;     /* لون التمييز */
}
```

### المنتجات
لإضافة منتجات جديدة، عدّل مصفوفة `products` في ملف `js/main.js`:

```javascript
const products = [
    {
        id: 7,
        name: "اسم المنتج الجديد",
        price: 100,
        image: "رابط الصورة",
        description: "وصف المنتج",
        colors: ["أسود", "أبيض"],
        sizes: ["S", "M", "L"]
    }
];
```

## 📱 التجاوب

الموقع مُحسّن للعمل على:
- **الكمبيوتر**: شاشات كبيرة (1200px+)
- **التابلت**: شاشات متوسطة (768px - 1199px)
- **الهاتف المحمول**: شاشات صغيرة (أقل من 768px)

## 🔧 التقنيات المستخدمة

- **HTML5**: هيكل الصفحات
- **CSS3**: التنسيقات والتأثيرات
- **Bootstrap 5**: إطار العمل للتصميم المتجاوب
- **Bootstrap Icons**: الأيقونات
- **JavaScript**: التفاعلية وإدارة العربة
- **localStorage**: حفظ بيانات العربة والطلبات

## 🌐 المتصفحات المدعومة

- Chrome (الإصدار الأحدث)
- Firefox (الإصدار الأحدث)
- Safari (الإصدار الأحدث)
- Edge (الإصدار الأحدث)

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966 50 123 4567

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

---

**ملاحظة**: هذا موقع تجريبي لأغراض التعلم والعرض. في التطبيق الحقيقي، ستحتاج إلى:
- خادم ويب
- قاعدة بيانات
- نظام دفع حقيقي
- نظام إدارة المحتوى
